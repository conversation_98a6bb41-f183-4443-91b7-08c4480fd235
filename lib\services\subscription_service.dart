/// 바라 부스 매니저 - 구독 관리 서비스
///
/// 사용자의 구독 상태를 관리하고 기능 제한을 처리하는 서비스입니다.
/// - 구독 플랜 관리
/// - 기능 제한 확인
/// - 구독 상태 업데이트
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/subscription_plan.dart';
import '../utils/logger_utils.dart';

/// 구독 관리 서비스
class SubscriptionService {
  static const String _tag = 'SubscriptionService';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  /// 현재 사용자 ID
  String? get _currentUserId => _auth.currentUser?.uid;
  
  /// 사용자가 로그인되어 있는지 확인
  bool get isUserLoggedIn => _currentUserId != null;

  /// 현재 사용자의 구독 정보 가져오기
  Future<UserSubscription?> getCurrentSubscription() async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logWarning('사용자가 로그인되어 있지 않음', tag: _tag);
        return null;
      }

      final doc = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .get();

      if (!doc.exists || doc.data() == null) {
        // 구독 정보가 없으면 무료 플랜으로 생성
        final freeSubscription = UserSubscription.createFree(userId: _currentUserId!);
        await _saveSubscription(freeSubscription);
        return freeSubscription;
      }

      return UserSubscriptionExtensions.fromFirebaseMap(doc.data()!);
    } catch (e, stackTrace) {
      LoggerUtils.logError('구독 정보 조회 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 구독 정보 저장
  Future<void> _saveSubscription(UserSubscription subscription) async {
    try {
      if (!isUserLoggedIn) {
        throw Exception('로그인이 필요합니다');
      }

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .set(subscription.toFirebaseMap());

      LoggerUtils.logInfo('구독 정보 저장 완료: ${subscription.planType}', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('구독 정보 저장 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 구독 플랜 변경
  Future<void> updateSubscriptionPlan(SubscriptionPlanType planType) async {
    try {
      if (!isUserLoggedIn) {
        throw Exception('로그인이 필요합니다');
      }

      final currentSubscription = await getCurrentSubscription();
      if (currentSubscription == null) {
        throw Exception('현재 구독 정보를 찾을 수 없습니다');
      }

      UserSubscription updatedSubscription;
      
      if (planType == SubscriptionPlanType.pro) {
        // 프로 플랜으로 업그레이드 (1개월 구독)
        final startDate = DateTime.now();
        final endDate = DateTime(startDate.year, startDate.month + 1, startDate.day);

        updatedSubscription = UserSubscription.createPro(
          userId: _currentUserId!,
          startDate: startDate,
          endDate: endDate,
        );
      } else if (planType == SubscriptionPlanType.plus) {
        // 플러스 플랜으로 업그레이드 (1개월 구독)
        final startDate = DateTime.now();
        final endDate = DateTime(startDate.year, startDate.month + 1, startDate.day);

        updatedSubscription = UserSubscription.createPlus(
          userId: _currentUserId!,
          startDate: startDate,
          endDate: endDate,
        );
      } else {
        // 무료 플랜으로 다운그레이드
        updatedSubscription = UserSubscription.createFree(userId: _currentUserId!);
      }

      await _saveSubscription(updatedSubscription);
      
      // 로컬 캐시도 업데이트
      await _updateLocalSubscriptionCache(planType);
      
      LoggerUtils.logInfo('구독 플랜 변경 완료: ${currentSubscription.planType} -> $planType', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('구독 플랜 변경 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 로컬 구독 캐시 업데이트
  Future<void> _updateLocalSubscriptionCache(SubscriptionPlanType planType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('cached_subscription_plan', planType.toString());
      LoggerUtils.logDebug('로컬 구독 캐시 업데이트: $planType', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('로컬 구독 캐시 업데이트 실패', tag: _tag, error: e);
    }
  }

  /// 로컬 캐시에서 구독 플랜 가져오기 (오프라인 지원)
  Future<SubscriptionPlanType> getCachedSubscriptionPlan() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedPlan = prefs.getString('cached_subscription_plan');
      
      if (cachedPlan != null) {
        return SubscriptionPlanType.values.firstWhere(
          (e) => e.toString() == cachedPlan,
          orElse: () => SubscriptionPlanType.free,
        );
      }
      
      return SubscriptionPlanType.free;
    } catch (e) {
      LoggerUtils.logError('로컬 구독 캐시 조회 실패', tag: _tag, error: e);
      return SubscriptionPlanType.free;
    }
  }

  /// 현재 구독 플랜 타입 가져오기 (오프라인 우선)
  Future<SubscriptionPlanType> getCurrentPlanType() async {
    try {
      // 🔥 로컬 캐시를 우선적으로 사용 (오프라인 지원)
      final cachedPlan = await getCachedSubscriptionPlan();
      LoggerUtils.logInfo('로컬 캐시에서 구독 플랜 조회: $cachedPlan', tag: _tag);

      // 백그라운드에서 서버와 동기화 (결과를 기다리지 않음)
      _syncWithServerInBackground();

      return cachedPlan;
    } catch (e) {
      LoggerUtils.logError('구독 플랜 타입 조회 실패', tag: _tag, error: e);
      // 모든 것이 실패하면 무료 플랜으로 폴백
      return SubscriptionPlanType.free;
    }
  }

  /// 백그라운드에서 서버와 동기화 (비동기)
  Future<void> _syncWithServerInBackground() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription != null && subscription.isValid) {
        await _updateLocalSubscriptionCache(subscription.planType);
        LoggerUtils.logInfo('백그라운드 서버 동기화 완료: ${subscription.planType}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logInfo('백그라운드 서버 동기화 실패 (정상적임): $e', tag: _tag);
      // 동기화 실패는 정상적인 상황 (오프라인 등)
    }
  }

  /// 구독 상태 강제 새로고침 (문제 해결용)
  Future<SubscriptionPlanType> forceRefreshSubscription() async {
    try {
      LoggerUtils.logInfo('구독 상태 강제 새로고침 시작', tag: _tag);

      // 서버에서 최신 구독 정보 가져오기
      final subscription = await getCurrentSubscription();
      if (subscription != null && subscription.isValid) {
        await _updateLocalSubscriptionCache(subscription.planType);
        LoggerUtils.logInfo('강제 새로고침 완료: ${subscription.planType}', tag: _tag);
        return subscription.planType;
      } else {
        // 서버에 구독 정보가 없으면 무료 플랜으로 설정
        await _updateLocalSubscriptionCache(SubscriptionPlanType.free);
        LoggerUtils.logInfo('서버에 구독 정보 없음 - 무료 플랜으로 설정', tag: _tag);
        return SubscriptionPlanType.free;
      }
    } catch (e) {
      LoggerUtils.logError('구독 상태 강제 새로고침 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 현재 구독 플랜 정보 가져오기
  Future<SubscriptionPlan> getCurrentPlan() async {
    final planType = await getCurrentPlanType();
    return PredefinedPlans.getPlan(planType);
  }

  /// 특정 기능이 사용 가능한지 확인
  Future<bool> hasFeature(String featureName) async {
    try {
      final plan = await getCurrentPlan();
      return plan.hasFeature(featureName);
    } catch (e) {
      LoggerUtils.logError('기능 사용 가능 여부 확인 실패: $featureName', tag: _tag, error: e);
      // 에러 발생 시 무료 플랜 기준으로 판단
      return PredefinedPlans.free.hasFeature(featureName);
    }
  }

  /// 행사 등록 제한 확인
  Future<bool> canCreateEvent(int currentEventCount) async {
    try {
      final plan = await getCurrentPlan();
      if (plan.maxEvents == null) return true; // 무제한
      return currentEventCount < plan.maxEvents!;
    } catch (e) {
      LoggerUtils.logError('행사 등록 제한 확인 실패', tag: _tag, error: e);
      return currentEventCount < PredefinedPlans.free.maxEvents!;
    }
  }

  /// 상품 등록 제한 확인
  Future<bool> canCreateProduct(int currentProductCount) async {
    try {
      final plan = await getCurrentPlan();
      if (plan.maxProducts == null) return true; // 무제한
      return currentProductCount < plan.maxProducts!;
    } catch (e) {
      LoggerUtils.logError('상품 등록 제한 확인 실패', tag: _tag, error: e);
      return currentProductCount < PredefinedPlans.free.maxProducts!;
    }
  }

  /// 구독 상태 실시간 구독 (읽기 사용량 최적화를 위해 비활성화)
  /// 필요할 때만 getCurrentSubscription()을 사용하도록 변경
  Stream<UserSubscription?> watchSubscription() {
    LoggerUtils.logInfo('실시간 구독 대신 필요할 때만 구독 상태를 읽도록 최적화됨', tag: _tag);
    return Stream.value(null);
  }

  /// 구독 만료 확인 및 자동 다운그레이드
  Future<void> checkAndHandleExpiredSubscription() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return;

      if (subscription.isExpired) {
        LoggerUtils.logInfo('구독이 만료되어 무료 플랜으로 다운그레이드합니다', tag: _tag);
        await updateSubscriptionPlan(SubscriptionPlanType.free);
      }
    } catch (e) {
      LoggerUtils.logError('구독 만료 확인 실패', tag: _tag, error: e);
    }
  }

  /// 구독 정보 초기화 (회원가입 시 호출)
  Future<void> initializeSubscriptionForNewUser() async {
    try {
      if (!isUserLoggedIn) {
        throw Exception('로그인이 필요합니다');
      }

      final freeSubscription = UserSubscription.createFree(userId: _currentUserId!);
      await _saveSubscription(freeSubscription);
      await _updateLocalSubscriptionCache(SubscriptionPlanType.free);

      LoggerUtils.logInfo('신규 사용자 구독 정보 초기화 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('신규 사용자 구독 정보 초기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 앱 시작시 로컬과 서버 플랜 동기화
  Future<void> syncPlanOnAppStart() async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logInfo('로그인되지 않은 상태 - 플랜 동기화 건너뛰기', tag: _tag);
        return;
      }

      // 로컬 캐시된 플랜 타입 가져오기
      final localPlanType = await getCachedSubscriptionPlan();

      // 서버에서 현재 구독 정보 가져오기
      final serverSubscription = await getCurrentSubscription();
      final serverPlanType = serverSubscription?.planType ?? SubscriptionPlanType.free;

      // 로컬과 서버 플랜이 다른 경우 서버 플랜으로 동기화
      if (localPlanType != serverPlanType) {
        LoggerUtils.logInfo('플랜 불일치 감지 - 로컬: $localPlanType, 서버: $serverPlanType', tag: _tag);
        await _updateLocalSubscriptionCache(serverPlanType);
        LoggerUtils.logInfo('로컬 플랜을 서버 플랜으로 동기화 완료: $serverPlanType', tag: _tag);
      } else {
        LoggerUtils.logInfo('로컬과 서버 플랜이 일치함: $localPlanType', tag: _tag);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError('앱 시작시 플랜 동기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 동기화 실패해도 앱 실행은 계속되도록 함
    }
  }

  /// 네트워크 연결시 플랜 변경 감지 및 동기화
  Future<void> checkPlanChangesOnNetworkReconnect() async {
    try {
      if (!isUserLoggedIn) {
        return;
      }

      LoggerUtils.logInfo('네트워크 재연결 - 플랜 변경 확인 시작', tag: _tag);

      // 서버에서 최신 구독 정보 가져오기
      final serverSubscription = await getCurrentSubscription();
      final serverPlanType = serverSubscription?.planType ?? SubscriptionPlanType.free;

      // 로컬 캐시와 비교
      final localPlanType = await getCachedSubscriptionPlan();

      if (localPlanType != serverPlanType) {
        LoggerUtils.logInfo('네트워크 재연결시 플랜 변경 감지 - 로컬: $localPlanType, 서버: $serverPlanType', tag: _tag);
        await _updateLocalSubscriptionCache(serverPlanType);
        LoggerUtils.logInfo('플랜 동기화 완료: $serverPlanType', tag: _tag);
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError('네트워크 재연결시 플랜 확인 실패', tag: _tag, error: e, stackTrace: stackTrace);
    }
  }
}
