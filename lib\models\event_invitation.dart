/// 바라 부스 매니저 - 행사 초대 데이터 모델
///
/// 행사 초대 정보를 표현하는 데이터 모델 클래스입니다.
/// - 초대 코드, 행사 정보, 초대자/피초대자 정보 등 포함
/// - Firebase Firestore 연동 지원
/// - freezed를 사용하여 불변 객체로 생성
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'invitation_status.dart';

part 'event_invitation.freezed.dart';


/// 초대를 수락한 사용자 정보
class InvitedUser {
  /// 사용자 ID
  final String userId;

  /// 사용자 닉네임
  final String nickname;

  /// 수락 시간
  final DateTime acceptedAt;

  const InvitedUser({
    required this.userId,
    required this.nickname,
    required this.acceptedAt,
  });

  /// Firebase 문서에서 안전하게 생성
  factory InvitedUser.fromFirebaseMap(Map<String, dynamic> map) {
    try {
      return InvitedUser(
        userId: map['userId'] as String,
        nickname: map['nickname'] as String,
        acceptedAt: DateTime.parse(map['acceptedAt'] as String),
      );
    } catch (e) {
      throw FormatException('Firebase InvitedUser 데이터 파싱 실패: $e\nData: $map');
    }
  }

  /// Firebase 저장용 Map 변환
  Map<String, dynamic> toFirebaseMap() {
    return {
      'userId': userId,
      'nickname': nickname,
      'acceptedAt': acceptedAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvitedUser &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          nickname == other.nickname &&
          acceptedAt == other.acceptedAt;

  @override
  int get hashCode => userId.hashCode ^ nickname.hashCode ^ acceptedAt.hashCode;

  @override
  String toString() => 'InvitedUser(userId: $userId, nickname: $nickname, acceptedAt: $acceptedAt)';
}

/// 행사 초대 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class EventInvitation with _$EventInvitation {
  const factory EventInvitation({
    /// 초대 고유 ID
    required String id,

    /// 초대 코드 (6자리 영숫자)
    required String invitationCode,

    /// 행사 ID
    required int eventId,

    /// 행사명 (캐시용)
    required String eventName,

    /// 행사 소유자 사용자 ID
    required String ownerUserId,

    /// 행사 소유자 닉네임 (캐시용)
    required String ownerNickname,

    /// 초대받은 사용자 ID (수락 후 설정됨) - 하위 호환성을 위해 유지
    String? invitedUserId,

    /// 초대받은 사용자 닉네임 (수락 후 설정됨) - 하위 호환성을 위해 유지
    String? invitedNickname,

    /// 초대를 수락한 사용자들 목록 (최대 3명) - Firebase에서만 사용
    @Default([]) List<InvitedUser> acceptedUsers,

    /// 초대 상태
    @Default(InvitationStatus.pending) InvitationStatus status,

    /// 초대 생성 시간
    required DateTime createdAt,

    /// 초대 만료 시간 (생성 후 2시간)
    required DateTime expiresAt,

    /// 초대 응답 시간 (수락/거절 시 설정됨)
    DateTime? respondedAt,
  }) = _EventInvitation;



  /// Firebase 문서에서 안전하게 생성
  factory EventInvitation.fromFirebaseMap(Map<String, dynamic> map) {
    try {
      // acceptedUsers 필드 파싱
      List<InvitedUser> acceptedUsers = [];
      if (map['acceptedUsers'] != null) {
        final usersData = map['acceptedUsers'] as List<dynamic>;
        acceptedUsers = usersData.map((userData) =>
          InvitedUser.fromFirebaseMap(userData as Map<String, dynamic>)
        ).toList();
      }

      return EventInvitation(
        id: map['id'] as String,
        invitationCode: map['invitationCode'] as String,
        eventId: map['eventId'] as int,
        eventName: map['eventName'] as String,
        ownerUserId: map['ownerUserId'] as String,
        ownerNickname: map['ownerNickname'] as String,
        invitedUserId: map['invitedUserId'] as String?,
        invitedNickname: map['invitedNickname'] as String?,
        acceptedUsers: acceptedUsers,
        status: InvitationStatus.fromString(map['status'] as String),
        createdAt: DateTime.parse(map['createdAt'] as String),
        expiresAt: DateTime.parse(map['expiresAt'] as String),
        respondedAt: map['respondedAt'] != null ? DateTime.parse(map['respondedAt'] as String) : null,
      );
    } catch (e) {
      throw FormatException('Firebase EventInvitation 데이터 파싱 실패: $e\nData: $map');
    }
  }

  /// 새 초대 생성을 위한 팩토리 생성자
  factory EventInvitation.create({
    required String invitationCode,
    required int eventId,
    required String eventName,
    required String ownerUserId,
    required String ownerNickname,
  }) {
    final now = DateTime.now();
    return EventInvitation(
      id: '${eventId}_${invitationCode}_${now.millisecondsSinceEpoch}',
      invitationCode: invitationCode,
      eventId: eventId,
      eventName: eventName,
      ownerUserId: ownerUserId,
      ownerNickname: ownerNickname,
      status: InvitationStatus.pending,
      createdAt: now,
      expiresAt: now.add(const Duration(hours: 2)), // 2시간 후 만료
    );
  }
}

/// EventInvitation 확장 메서드
extension EventInvitationExtension on EventInvitation {
  /// Firebase 업로드용 맵 변환
  Map<String, dynamic> toFirebaseMap() {
    final map = <String, dynamic>{
      'id': id,
      'invitationCode': invitationCode,
      'eventId': eventId,
      'eventName': eventName,
      'ownerUserId': ownerUserId,
      'ownerNickname': ownerNickname,
      'status': status.value,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'acceptedUsers': acceptedUsers.map((user) => user.toFirebaseMap()).toList(),
    };

    // 선택적 필드들 (null이 아닌 경우만 포함)
    if (invitedUserId != null) {
      map['invitedUserId'] = invitedUserId;
    }
    if (invitedNickname != null) {
      map['invitedNickname'] = invitedNickname;
    }
    if (respondedAt != null) {
      map['respondedAt'] = respondedAt!.toIso8601String();
    }

    return map;
  }

  /// 초대가 만료되었는지 확인
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// 초대가 활성 상태인지 확인 (대기 중이고 만료되지 않음)
  bool get isActive => status == InvitationStatus.pending && !isExpired;

  /// 초대가 완료된 상태인지 확인 (수락됨)
  bool get isCompleted => status == InvitationStatus.accepted;

  /// 현재 수락한 사용자 수
  int get acceptedCount => acceptedUsers.length;

  /// 더 많은 사용자가 수락할 수 있는지 확인 (최대 3명)
  bool get canAcceptMore => acceptedCount < 3;

  /// 특정 사용자가 이미 수락했는지 확인
  bool hasUserAccepted(String userId) {
    return acceptedUsers.any((user) => user.userId == userId);
  }
}


