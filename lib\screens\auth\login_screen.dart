import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../providers/nickname_provider.dart';
import '../../providers/data_sync_provider.dart';
import '../../services/admin_service.dart';
import '../../services/subscription_service.dart';
import '../../models/subscription_plan.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/onboarding_components.dart';
import 'forgot_password_screen.dart';
import 'terms_agreement_screen.dart';


/// 로그인 화면 - 웜톤 디자인으로 개선
///
/// 카드 기반 레이아웃과 원형 소셜 로그인 버튼을 적용한 현대적 디자인
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class LoginScreen extends ConsumerStatefulWidget {
  final void Function(bool hasServerData) onLoginSuccess;
  final VoidCallback onRegisterRequested;
  const LoginScreen({super.key, required this.onLoginSuccess, required this.onRegisterRequested});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  // FocusNode 추가
  final FocusNode emailFocus = FocusNode();
  final FocusNode passwordFocus = FocusNode();
  bool isLoading = false;
  String? error;
  String? infoMessage;
  bool showResendButton = false;

  @override
  void initState() {
    super.initState();
    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    emailFocus.dispose();
    passwordFocus.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    setState(() { isLoading = true; error = null; infoMessage = null; showResendButton = false; });
    if (emailController.text.trim().isEmpty) {
      setState(() { error = '이메일을 입력하세요.'; isLoading = false; });
      return;
    }
    if (passwordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호를 입력하세요.'; isLoading = false; });
      return;
    }
    try {
      final auth = FirebaseAuth.instance;
      final userCred = await auth.signInWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );
      // 안전한 null 체크
      final user = userCred.user;
      if (user == null) {
        setState(() { error = '사용자 정보를 가져올 수 없습니다.'; });
        return;
      }

      if (!user.emailVerified) {
        setState(() {
          error = '이메일 인증을 완료해야 합니다.';
          showResendButton = true;
        });
        await auth.signOut();
      } else {
        // 로그인 성공 시 이메일 인증 대기 상태 해제
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('email_verification_pending');

        // 로그인 성공 시 사용자 데이터 초기화 및 검증
        try {
          // 사용자 문서가 존재하는지 확인하고 없으면 생성
          await _ensureUserDocumentExists(user);

          // nicknameProvider 동기화
          await ref.read(nicknameProvider.notifier).loadNickname();

          // 닉네임 전용 동기화 (플랜에 관계없이 항상 실행)
          await ref.read(nicknameProvider.notifier).syncNicknameOnly();

          // 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
          final hasServerData = await _checkServerDataAndSetSyncFlag();

          // 접속 로그 기록 (안전한 null 체크)
          try {
            await AdminService.logAccess(user.uid, null);
          } catch (e) {
            LoggerUtils.logWarning('접속 로그 기록 실패', tag: 'LoginScreen', error: e);
          }

          widget.onLoginSuccess(hasServerData);
        } catch (e) {
          LoggerUtils.logError('로그인 후 사용자 데이터 초기화 실패', tag: 'LoginScreen', error: e);
          setState(() {
            error = '로그인 처리 중 오류가 발생했습니다: ${e.toString()}';
          });
          // 로그인 실패 시 로그아웃
          await FirebaseAuth.instance.signOut();
        }
      }
    } on FirebaseAuthException catch (e) {
      setState(() { error = _firebaseErrorToKorean(e); });
    } catch (e) {
      LoggerUtils.logError('로그인 처리 중 오류 발생', tag: 'LoginScreen', error: e);
      setState(() { error = '로그인 처리 중 오류가 발생했습니다: ${e.toString()}'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  /// 서버 데이터 존재 여부 확인 및 동기화 플래그 설정 (플랜별 처리)
  Future<bool> _checkServerDataAndSetSyncFlag() async {
    try {
      // 현재 사용자의 구독 플랜 확인
      final subscriptionService = SubscriptionService();
      final currentPlan = await subscriptionService.getCurrentPlan();
      final planType = await subscriptionService.getCurrentPlanType();

      LoggerUtils.logInfo('현재 구독 플랜: ${currentPlan.name} (타입: $planType)', tag: 'LoginScreen');

      // 🔥 프리플랜, 플러스 플랜은 서버 동기화 기능이 없으므로 무조건 스킵
      if (planType == SubscriptionPlanType.free || planType == SubscriptionPlanType.plus || !currentPlan.hasServerSyncFeature) {
        LoggerUtils.logInfo('${currentPlan.name}은 서버 동기화 기능이 없어 동기화 플로우를 스킵합니다 (플랜타입: $planType)', tag: 'LoginScreen');

        // SharedPreferences에 플래그 저장 (동기화 불필요)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_server_data_on_login', false);

        return false; // 동기화 화면으로 이동하지 않음
      }

      // 🔥 프로 플랜이 아닌 경우 추가 안전장치
      if (planType != SubscriptionPlanType.pro) {
        LoggerUtils.logWarning('프로 플랜이 아닌데 서버 동기화 기능이 활성화됨 - 강제로 스킵: $planType', tag: 'LoginScreen');
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('has_server_data_on_login', false);
        return false;
      }

      // 프로 플랜만 서버 데이터 확인 및 동기화 수행
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();

      LoggerUtils.logInfo('로그인 시 서버 데이터 확인: $hasServerData', tag: 'LoginScreen');
      LoggerUtils.logInfo('hasServerData() 메서드 결과: $hasServerData', tag: 'LoginScreen');

      // SharedPreferences에 서버 데이터 존재 여부 저장 (main.dart에서 사용)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', hasServerData);

      if (hasServerData) {
        LoggerUtils.logInfo('서버 데이터 존재 - 동기화 확인 필요', tag: 'LoginScreen');
      } else {
        LoggerUtils.logInfo('서버 데이터 없음 - 정상 온보딩 진행', tag: 'LoginScreen');
      }
      return hasServerData;
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'LoginScreen', error: e);
      // 확인 실패 시 안전하게 false로 설정
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_server_data_on_login', false);
      return false;
    }
  }







  Future<void> _handleResendEmail() async {
    setState(() { isLoading = true; infoMessage = null; });
    try {
      final auth = FirebaseAuth.instance;
      final user = await auth.signInWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );
      if (user.user != null && !user.user!.emailVerified) {
        await user.user!.sendEmailVerification();
        setState(() {
          infoMessage = '인증 메일이 재발송되었습니다. 메일함을 확인해 주세요.';
          showResendButton = false;
        });
        await auth.signOut();
      } else {
        setState(() {
          infoMessage = '이미 인증이 완료된 계정입니다. 다시 로그인해 주세요.';
          showResendButton = false;
        });
      }
    } catch (e) {
      setState(() { infoMessage = '인증 메일 재발송 중 오류가 발생했습니다.'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  Future<void> _handleForgotPassword() async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ForgotPasswordScreen(),
      ),
    );
  }

  String _firebaseErrorToKorean(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-email':
        return '올바른 이메일 형식이 아닙니다.';
      case 'user-not-found':
        return '해당 이메일로 가입된 계정이 없습니다.';
      case 'wrong-password':
        return '비밀번호가 올바르지 않습니다.';
      case 'too-many-requests':
        return '잠시 후 다시 시도해 주세요.';
      case 'network-request-failed':
        return '네트워크 오류가 발생했습니다.';
      case 'user-disabled':
        return '이 계정은 비활성화되어 있습니다.';
      default:
        return '입력 정보를 다시 확인해 주세요.';
    }
  }

  // 구글 로그인 메서드
  Future<void> _handleGoogleSignIn() async {
    setState(() { isLoading = true; error = null; });
    try {
      final GoogleSignIn signIn = GoogleSignIn.instance;

      // 릴리즈 모드 대응: 초기화 전 로그아웃 처리
      try {
        await signIn.signOut();
        LoggerUtils.logInfo('Google Sign-In 이전 세션 정리 완료', tag: 'LoginScreen');
      } catch (e) {
        LoggerUtils.logInfo('Google Sign-In 이전 세션 정리 건너뜀', tag: 'LoginScreen');
      }

      await signIn.initialize(
        serverClientId: '************-0kgl0snkmk62ecrfkmoka6jq3dscj4he.apps.googleusercontent.com',
      );

      final GoogleSignInAccount? googleUser = await signIn.authenticate();
      if (googleUser == null) {
        setState(() { error = '구글 로그인이 취소되었습니다.'; });
        return;
      }

      LoggerUtils.logInfo('GoogleSignInAccount 정보 - Email: ${googleUser.email}, DisplayName: ${googleUser.displayName}', tag: 'LoginScreen');

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // 토큰 유효성 검사
      if (googleAuth.idToken == null) {
        setState(() { error = '구글 인증 토큰을 가져올 수 없습니다.'; });
        return;
      }

      final credential = GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
      );

      LoggerUtils.logInfo('Firebase 인증 시작...', tag: 'LoginScreen');
      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Firebase 인증 상태 동기화 대기
      await Future.delayed(const Duration(milliseconds: 500));

      final user = userCredential.user;
      if (user == null) {
        throw Exception('사용자 정보를 가져올 수 없습니다.');
      }

      // 이메일 정보 확인 및 로깅
      final firebaseEmail = user.email;
      final googleEmail = googleUser.email;

      LoggerUtils.logInfo('구글 로그인 이메일 정보 비교:', tag: 'LoginScreen');
      LoggerUtils.logInfo('- Firebase User Email: $firebaseEmail', tag: 'LoginScreen');
      LoggerUtils.logInfo('- GoogleSignInAccount Email: $googleEmail', tag: 'LoginScreen');

      // 사용할 이메일 결정 (GoogleSignInAccount 이메일 우선)
      final finalEmail = googleEmail;
      LoggerUtils.logInfo('- 최종 사용할 이메일: $finalEmail', tag: 'LoginScreen');

      LoggerUtils.logInfo('구글 로그인 성공: $finalEmail', tag: 'LoginScreen');
      LoggerUtils.logInfo('구글 로그인 사용자 정보 - UID: ${user.uid}, Email: $finalEmail, DisplayName: ${user.displayName}', tag: 'LoginScreen');

      // 신규 사용자인지 확인 및 약관 동의 여부 확인
      final additionalUserInfo = userCredential.additionalUserInfo;
      final isNewUser = additionalUserInfo?.isNewUser ?? false;

      LoggerUtils.logInfo('구글 로그인 사용자 타입: ${isNewUser ? "신규" : "기존"} 사용자', tag: 'LoginScreen');

      // 기존 사용자인 경우 약관 동의 여부 확인
      bool needsTermsAgreement = isNewUser;
      if (!isNewUser) {
        try {
          final userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();

          final agreementData = userDoc.data()?['agreement'] as Map<String, dynamic>?;
          final hasAgreed = agreementData?['agreed'] as bool? ?? false;

          if (!hasAgreed) {
            needsTermsAgreement = true;
            LoggerUtils.logInfo('기존 사용자이지만 약관 동의 기록이 없음', tag: 'LoginScreen');
          }
        } catch (e) {
          LoggerUtils.logWarning('약관 동의 여부 확인 실패, 약관 동의 페이지로 이동', tag: 'LoginScreen', error: e);
          needsTermsAgreement = true;
        }
      }

      if (needsTermsAgreement) {
        // 신규 사용자 또는 약관 동의가 필요한 기존 사용자 - 약관 동의 페이지로 이동
        LoggerUtils.logInfo('약관 동의 페이지로 이동 (신규: $isNewUser)', tag: 'LoginScreen');
        setState(() { isLoading = false; });

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TermsAgreementScreen(
              email: finalEmail,
              password: '', // 소셜 로그인이므로 비밀번호 없음
              phone: '', // 소셜 로그인이므로 전화번호 없음
              isGoogleLogin: true,
              isAppleLogin: false,
              socialUserCredential: userCredential,
              onBackToLogin: () {
                Navigator.of(context).pop();
              },
              onLoginSuccess: widget.onLoginSuccess,
            ),
          ),
        );
        return;
      } else {
        // 기존 사용자 - 바로 로그인 처리
        LoggerUtils.logInfo('기존 구글 사용자 - 바로 로그인 처리', tag: 'LoginScreen');
        await _handleSocialLoginSuccess(user, finalEmail);
      }
    } catch (e) {
      LoggerUtils.logError('구글 로그인 실패', tag: 'LoginScreen', error: e);
      setState(() { error = '구글 로그인 실패: $e'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  Future<void> _handleAppleSignIn() async {
    setState(() { isLoading = true; error = null; });
    try {
      LoggerUtils.logInfo('애플 로그인 시작 (개선된 방식)', tag: 'LoginScreen');

      // 기존 진행 중인 Apple 로그인이 있다면 취소
      try {
        await FirebaseAuth.instance.signOut();
        LoggerUtils.logInfo('기존 Firebase 세션 정리 완료', tag: 'LoginScreen');
      } catch (e) {
        LoggerUtils.logInfo('기존 Firebase 세션 정리 건너뜀', tag: 'LoginScreen');
      }

      // Apple Auth Provider 생성
      LoggerUtils.logInfo('AppleAuthProvider 생성 중...', tag: 'LoginScreen');
      final appleProvider = AppleAuthProvider();

      // 스코프 추가 (FlutterFire에서는 addScope 메서드 사용)
      appleProvider.addScope('email');
      appleProvider.addScope('name');

      // 커스텀 파라미터 설정 (한국어 지원)
      appleProvider.setCustomParameters({
        'locale': 'ko',
      });

      LoggerUtils.logInfo('Apple Auth Provider 설정 완료', tag: 'LoginScreen');
      LoggerUtils.logInfo('- Provider: AppleAuthProvider', tag: 'LoginScreen');
      LoggerUtils.logInfo('- Scopes: email, name', tag: 'LoginScreen');
      LoggerUtils.logInfo('- Custom Parameters: locale=ko', tag: 'LoginScreen');

      // Flutter에서는 signInWithProvider 사용 (네이티브 플랫폼용)
      LoggerUtils.logInfo('Apple 로그인 시작 (signInWithProvider)...', tag: 'LoginScreen');

      // 중복 요청 방지를 위한 더 짧은 타임아웃과 재시도 로직
      UserCredential? userCredential;
      int retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          userCredential = await FirebaseAuth.instance.signInWithProvider(appleProvider).timeout(
            const Duration(seconds: 60), // 타임아웃을 60초로 단축
            onTimeout: () {
              LoggerUtils.logError('애플 로그인 타임아웃 (60초) - 재시도 ${retryCount + 1}/${maxRetries + 1}', tag: 'LoginScreen');
              throw Exception('애플 로그인 요청이 시간 초과되었습니다.');
            },
          );
          break; // 성공하면 루프 탈출
        } catch (e) {
          retryCount++;
          if (retryCount > maxRetries) {
            rethrow; // 최대 재시도 횟수 초과 시 에러 전파
          }

          LoggerUtils.logWarning('애플 로그인 실패, 재시도 중... (${retryCount}/${maxRetries + 1})', tag: 'LoginScreen', error: e);

          // 재시도 전 잠시 대기
          await Future.delayed(const Duration(seconds: 2));

          // Firebase 세션 정리 후 재시도
          try {
            await FirebaseAuth.instance.signOut();
          } catch (_) {}
        }
      }

      if (userCredential == null) {
        throw Exception('Apple 로그인이 취소되었거나 실패했습니다.');
      }

      LoggerUtils.logInfo('Firebase Auth Apple 로그인 완료!', tag: 'LoginScreen');
      await _processAppleSignInResult(userCredential);
    } catch (e, stackTrace) {
      LoggerUtils.logError('애플 로그인 실패', tag: 'LoginScreen', error: e, stackTrace: stackTrace);

      // 오류 타입별 상세 분석
      LoggerUtils.logInfo('오류 상세 분석:', tag: 'LoginScreen');
      LoggerUtils.logInfo('- 오류 타입: ${e.runtimeType}', tag: 'LoginScreen');
      LoggerUtils.logInfo('- 오류 메시지: ${e.toString()}', tag: 'LoginScreen');

      String errorMessage = '애플 로그인 실패';
      if (e.toString().contains('isAvailable')) {
        errorMessage = '이 기기에서는 애플 로그인을 사용할 수 없습니다.';
      } else if (e.toString().contains('canceled') || e.toString().contains('cancelled')) {
        errorMessage = '애플 로그인이 취소되었습니다.';
      } else if (e.toString().contains('timeout') || e.toString().contains('시간 초과')) {
        errorMessage = '애플 로그인 요청이 시간 초과되었습니다. 다시 시도해주세요.';
      } else if (e.toString().contains('missing initial state')) {
        errorMessage = '애플 로그인 설정 오류입니다. 잠시 후 다시 시도해주세요.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = '네트워크 연결을 확인하고 다시 시도해주세요.';
      } else {
        errorMessage = '애플 로그인 실패: ${e.toString()}';
      }

      LoggerUtils.logInfo('사용자에게 표시할 오류 메시지: $errorMessage', tag: 'LoginScreen');
      setState(() { error = errorMessage; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  Future<void> _processAppleSignInResult(UserCredential userCredential) async {
    // Firebase 인증 상태 동기화 대기
    await Future.delayed(const Duration(milliseconds: 500));

    final user = userCredential.user;
    if (user == null) {
      throw Exception('사용자 정보를 가져올 수 없습니다.');
    }

    // 추가 사용자 정보 확인 (Apple 로그인 특화)
    final additionalUserInfo = userCredential.additionalUserInfo;
    LoggerUtils.logInfo('Apple 로그인 추가 정보:', tag: 'LoginScreen');
    LoggerUtils.logInfo('- isNewUser: ${additionalUserInfo?.isNewUser}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- providerId: ${additionalUserInfo?.providerId}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- profile: ${additionalUserInfo?.profile}', tag: 'LoginScreen');

    // 사용자 정보 로깅
    LoggerUtils.logInfo('Apple 로그인 사용자 정보:', tag: 'LoginScreen');
    LoggerUtils.logInfo('- UID: ${user.uid}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- Email: ${user.email}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- DisplayName: ${user.displayName}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- PhotoURL: ${user.photoURL}', tag: 'LoginScreen');
    LoggerUtils.logInfo('- EmailVerified: ${user.emailVerified}', tag: 'LoginScreen');

    // Apple 로그인에서 이메일 추출 (여러 소스에서 시도)
    String? finalEmail = user.email;

    // 1. Firebase User에서 이메일 확인
    if (finalEmail == null || finalEmail.isEmpty) {
      LoggerUtils.logInfo('Firebase User에서 이메일을 찾을 수 없음, 추가 정보에서 확인 중...', tag: 'LoginScreen');

      // 2. additionalUserInfo.profile에서 이메일 확인
      final profile = additionalUserInfo?.profile;
      if (profile != null) {
        final profileMap = profile as Map<String, dynamic>?;
        finalEmail = profileMap?['email'] as String?;
        LoggerUtils.logInfo('Profile에서 찾은 이메일: $finalEmail', tag: 'LoginScreen');
      }
    }

    // 3. providerData에서 이메일 확인
    if (finalEmail == null || finalEmail.isEmpty) {
      LoggerUtils.logInfo('Profile에서도 이메일을 찾을 수 없음, providerData에서 확인 중...', tag: 'LoginScreen');
      for (final providerProfile in user.providerData) {
        if (providerProfile.providerId == 'apple.com' && providerProfile.email != null) {
          finalEmail = providerProfile.email;
          LoggerUtils.logInfo('ProviderData에서 찾은 이메일: $finalEmail', tag: 'LoginScreen');
          break;
        }
      }
    }

    LoggerUtils.logInfo('최종 결정된 이메일: $finalEmail', tag: 'LoginScreen');
    LoggerUtils.logInfo('애플 로그인 성공: $finalEmail', tag: 'LoginScreen');

    // 신규 사용자인지 확인 및 약관 동의 여부 확인
    final isNewUser = additionalUserInfo?.isNewUser ?? false;

    LoggerUtils.logInfo('애플 로그인 사용자 타입: ${isNewUser ? "신규" : "기존"} 사용자', tag: 'LoginScreen');

    // 기존 사용자인 경우 약관 동의 여부 확인
    bool needsTermsAgreement = isNewUser;
    if (!isNewUser) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        final agreementData = userDoc.data()?['agreement'] as Map<String, dynamic>?;
        final hasAgreed = agreementData?['agreed'] as bool? ?? false;

        if (!hasAgreed) {
          needsTermsAgreement = true;
          LoggerUtils.logInfo('기존 사용자이지만 약관 동의 기록이 없음', tag: 'LoginScreen');
        }
      } catch (e) {
        LoggerUtils.logWarning('약관 동의 여부 확인 실패, 약관 동의 페이지로 이동', tag: 'LoginScreen', error: e);
        needsTermsAgreement = true;
      }
    }

    if (needsTermsAgreement) {
      // 신규 사용자 또는 약관 동의가 필요한 기존 사용자 - 약관 동의 페이지로 이동
      LoggerUtils.logInfo('약관 동의 페이지로 이동 (신규: $isNewUser)', tag: 'LoginScreen');

      // Apple 로그인 정보를 유지하기 위해 signOut() 제거
      // 기존: await FirebaseAuth.instance.signOut(); // 이 줄을 제거하여 인증 상태 유지

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TermsAgreementScreen(
            email: finalEmail ?? '',
            password: '', // 소셜 로그인이므로 비밀번호 없음
            phone: '', // 소셜 로그인이므로 전화번호 없음
            isGoogleLogin: false,
            isAppleLogin: true,
            socialUserCredential: userCredential,
            onBackToLogin: () {
              Navigator.of(context).pop();
            },
            onLoginSuccess: widget.onLoginSuccess,
          ),
        ),
      );
    } else {
      // 기존 사용자 - 바로 로그인 처리
      LoggerUtils.logInfo('기존 애플 사용자 - 바로 로그인 처리', tag: 'LoginScreen');
      await _handleSocialLoginSuccess(user, finalEmail);
    }
  }

  /// 사용자 문서가 존재하는지 확인하고 없으면 생성
  Future<void> _ensureUserDocumentExists(User user) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        LoggerUtils.logInfo('사용자 문서가 존재하지 않아 생성합니다: ${user.uid}', tag: 'LoginScreen');

        // 기본 사용자 문서 생성
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .set({
          'email': user.email ?? '',
          'displayName': user.displayName ?? '',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'phoneVerified': false,
        }, SetOptions(merge: true));

        LoggerUtils.logInfo('사용자 문서 생성 완료: ${user.uid}', tag: 'LoginScreen');
      } else {
        LoggerUtils.logInfo('사용자 문서가 이미 존재합니다: ${user.uid}', tag: 'LoginScreen');
      }
    } catch (e) {
      LoggerUtils.logError('사용자 문서 확인/생성 실패', tag: 'LoginScreen', error: e);
      rethrow;
    }
  }

  /// 소셜 로그인 성공 후 공통 처리
  Future<void> _handleSocialLoginSuccess(User user, String? email) async {
    try {
      // 사용자 문서가 존재하는지 확인하고 없으면 생성
      await _ensureUserDocumentExists(user);

      // 로그인 성공 시 nicknameProvider 동기화
      await ref.read(nicknameProvider.notifier).loadNickname();

      // 닉네임 전용 동기화 (플랜에 관계없이 항상 실행)
      await ref.read(nicknameProvider.notifier).syncNicknameOnly();

      // 서버 데이터 존재 여부 확인 및 동기화 플래그 설정
      final hasServerData = await _checkServerDataAndSetSyncFlag();

      // 접속 로그 기록
      try {
        await AdminService.logAccess(user.uid, null);
      } catch (e) {
        LoggerUtils.logWarning('접속 로그 기록 실패', tag: 'LoginScreen', error: e);
      }

      widget.onLoginSuccess(hasServerData);
    } catch (e) {
      LoggerUtils.logError('소셜 로그인 후처리 실패', tag: 'LoginScreen', error: e);
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: OnboardingComponents.buildCard(
                context: context,
                child: _buildLoginForm(context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 로그인 폼 구성
  Widget _buildLoginForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 헤더
        _buildHeader(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 이메일 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: emailController,
          focusNode: emailFocus,
          label: '이메일',
          hint: '<EMAIL>',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onSubmitted: (_) => FocusScope.of(context).requestFocus(passwordFocus),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: passwordController,
          focusNode: passwordFocus,
          label: '비밀번호',
          prefixIcon: Icons.lock_outline,
          obscureText: true,
          textInputAction: TextInputAction.done,
          onSubmitted: (_) => _handleLogin(),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 에러 및 정보 메시지
        _buildMessages(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 로그인 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '로그인',
          onPressed: _handleLogin,
          isLoading: isLoading,
          icon: Icons.login,
        ),

        // 간결한 회원가입/비밀번호 찾기 링크
        _buildCompactAuthLinks(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 구분선
        _buildDivider(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 소셜 로그인
        _buildSocialLogin(context),

        OnboardingComponents.buildSmallSpacing(context),
      ],
    );
  }

  /// 간결한 회원가입/비밀번호 찾기 링크
  Widget _buildCompactAuthLinks(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: widget.onRegisterRequested,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            '회원가입',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context),
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.7),
            ),
          ),
        ),
        Text(
          '|',
          style: TextStyle(
            fontSize: ResponsiveHelper.getBodyFontSize(context),
            color: AppColors.onboardingTextSecondary.withValues(alpha: 0.5),
          ),
        ),
        TextButton(
          onPressed: _handleForgotPassword,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            '비밀번호 찾기',
            style: TextStyle(
              fontSize: ResponsiveHelper.getBodyFontSize(context),
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.7),
            ),
          ),
        ),
      ],
    );
  }

  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 로고 아이콘
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            boxShadow: [
              BoxShadow(
                color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.login_rounded,
            size: 30,
            color: AppColors.onboardingTextOnPrimary,
          ),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '로그인',
        ),

        const SizedBox(height: 8),

        // 부제목
        OnboardingComponents.buildBody(
          context: context,
          text: '바라부스매니저에 오신 것을 환영합니다',
        ),
      ],
    );
  }

  /// 에러 및 정보 메시지
  Widget _buildMessages(BuildContext context) {
    if (error == null && infoMessage == null && !showResendButton) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        if (error != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.errorLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: AppColors.error, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    error!,
                    style: TextStyle(
                      color: AppColors.error,
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],

        if (infoMessage != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.infoLight.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    infoMessage!,
                    style: TextStyle(
                      color: AppColors.info,
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],

        if (showResendButton) ...[
          OnboardingComponents.buildSecondaryButton(
            context: context,
            text: '인증 메일 재발송',
            onPressed: isLoading ? null : _handleResendEmail,
            icon: Icons.email_outlined,
          ),
          const SizedBox(height: 8),
        ],
      ],
    );
  }

  /// 구분선
  Widget _buildDivider(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.secondary,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            '또는',
            style: TextStyle(
              color: AppColors.onboardingTextSecondary,
              fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: AppColors.secondary,
          ),
        ),
      ],
    );
  }

  /// 소셜 로그인 섹션 (구글/애플 로그인 버튼)
  Widget _buildSocialLogin(BuildContext context) {
    return Column(
      children: [
        // 구글 로그인 버튼
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton.icon(
            onPressed: isLoading ? null : _handleGoogleSignIn,
            icon: Image.asset(
              'assets/icons/Google_logo.png',
              width: 24,
              height: 24,
            ),
            label: const Text('Google로 로그인'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
              side: BorderSide(color: Colors.grey.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 1,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // 애플 로그인 버튼
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton.icon(
            onPressed: isLoading ? null : _handleAppleSignIn,
            icon: Image.asset(
              'assets/icons/Apple_logo.png',
              width: 24,
              height: 24,
            ),
            label: const Text('Apple로 로그인'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
              side: BorderSide(color: Colors.grey.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 1,
            ),
          ),
        ),
      ],
    );
  }



}
